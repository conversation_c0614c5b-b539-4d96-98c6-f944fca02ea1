# SGN Core Configuration Example
# Copy this file to the root directory as .env and fill in your values

# GitHub Personal Access Token (required for seeding from GitHub)
# Get one at: https://github.com/settings/tokens
GH_TOKEN="your_github_token_here"

# SGN Daemon Configuration
SGN_DAEMON="http://localhost:8787"
SGN_HTTP_PORT=8787

# Broadcasting (set to 'off' for single-node development)
SGN_BROADCAST="off"

# Storage Paths (relative to project root)
SGN_DB="./data/sgn.db.json"
SGN_KUS_DIR="./data/kus"
SGN_LOGS_DIR="./logs"

# Trust Configuration
SGN_TRUST_PATH="./config/trust.json.example"

# Development Settings
NODE_ENV="development"
