---
name: Bug report
about: Create a report to help us improve SGN Core
title: ''
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Start daemon with '...'
2. Run command '....'
3. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Environment (please complete the following information):**
 - OS: [e.g. macOS, Ubuntu]
 - Node.js version: [e.g. 20.x]
 - SGN Core version: [e.g. 0.1.0-alpha.3]

**Logs**
If applicable, add logs to help explain your problem.

**Additional context**
Add any other context about the problem here.
