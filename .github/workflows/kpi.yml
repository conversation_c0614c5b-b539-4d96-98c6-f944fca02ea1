name: daily-kpi
on:
  schedule: [{ cron: '0 2 * * *' }]
  workflow_dispatch:

jobs:
  kpi:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with: { node-version: '20' }
      - run: npm ci
      - run: node sgn-poc/scripts/kpi-report.js
      - uses: actions/upload-artifact@v4
        with: { name: kpi-report, path: sgn-poc/reports/*.json }

