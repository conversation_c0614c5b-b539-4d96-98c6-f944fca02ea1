name: smoke-post-merge
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  smoke:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install deps
        run: npm ci

      - name: Run tests
        run: |
          npm test
          node --test sgn-poc/test/events-minimal-e2e.test.mjs
          node --test sgn-poc/test/events-slow-client-e2e.test.mjs

      - name: Start daemon (background)
        run: |
          SGN_DB=./sgn.ci.db SGN_HTTP_PORT=8787 \
          node sgn-poc/src/daemon/daemon.mjs > daemon.log 2>&1 &
          echo $! > daemon.pid

      - name: Health check (retry)
        run: |
          for i in {1..20}; do
            curl -sf http://localhost:8787/health && exit 0
            sleep 0.3
          done
          echo "health check failed" && exit 1

      - name: Show health
        run: curl -s http://localhost:8787/health | jq .

      - name: Grab metrics (prom)
        run: curl -s http://localhost:8787/metrics?format=prom > prom.txt

      - uses: actions/upload-artifact@v4
        with:
          name: prom-metrics
          path: prom.txt

      - name: Upload daemon log on failure
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: daemon-log
          path: daemon.log

      - name: Stop daemon
        if: always()
        run: |
          if [ -f daemon.pid ]; then kill $(cat daemon.pid) || true; fi

