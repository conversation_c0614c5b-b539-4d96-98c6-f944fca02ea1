# Contributing to SGN Core

We welcome contributions to SGN Core! This document provides guidelines for contributing to the project.

## Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/your-username/sgn-core.git`
3. Install dependencies: `npm install`
4. Start the daemon: `npm start`
5. Run tests: `npm test`

## Development Workflow

1. Create a feature branch: `git checkout -b feature/your-feature-name`
2. Make your changes
3. Add tests for new functionality
4. Run the test suite: `npm test`
5. Run smoke tests: `npm run smoke`
6. Commit your changes with a clear message
7. Push to your fork and create a Pull Request

## Code Style

- Use ES modules (import/export)
- Follow existing code formatting
- Add JSDoc comments for public APIs
- Keep functions small and focused

## Testing

- Add unit tests for new functionality in `test/`
- Run `npm test` before submitting PRs
- Use `npm run test:e2e:serial` for end-to-end tests
- Ensure smoke tests pass: `npm run smoke`

## Pull Request Guidelines

- Keep PRs small and focused
- Include a clear description of changes
- Reference related issues
- Ensure all tests pass
- Update documentation if needed

## Reporting Issues

- Use the issue templates provided
- Include reproduction steps
- Provide environment details
- Add relevant logs or error messages

## Questions?

Feel free to open an issue for questions or join discussions in existing issues.

Thank you for contributing to SGN Core!
