# SGN Core — Socrate Global Network

[![License: MIT](https://img.shields.io/badge/License-MIT-black.svg)](LICENSE)
![Status](https://img.shields.io/badge/status-PoC-blue)
![Node](https://img.shields.io/badge/node-%3E%3D20.x-informational)

**SGN Core** is a peer-to-peer (P2P) protocol and reference implementation to share
**Knowledge Units (KUs)** — compact, verifiable chunks of know-how (e.g., bugfixes, security notes,
playbooks) — **without central servers**.

> **Status:** Proof-of-Concept focused on developer UX, observability, and end-to-end flow
> (seed → publish → persist → broadcast → retrieve).

## Quick Start

```bash
# Clone and install
git clone https://github.com/JoyciAkira/sgn-core.git
cd sgn-core/sgn-poc
npm install

# Start the daemon
npm start

# Run smoke test
npm run smoke
```

## Documentation

📖 **[Complete Documentation](sgn-poc/README.md)** - Full setup, APIs, metrics, and usage guide

📋 **[Contributing Guide](sgn-poc/CONTRIBUTING.md)** - How to contribute to SGN Core

🗺️ **[Roadmap](sgn-poc/docs/SGN-ROADMAP.md)** - Development phases and milestones

🔧 **[Technical Guide](sgn-poc/docs/SGN-TECHNICAL-GUIDE.md)** - Architecture and implementation details

## Repository Structure

```
sgn-core/
├── sgn-poc/           # Main implementation and documentation
├── README.md          # This file
└── LICENSE            # MIT License
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Socrate Global Network** - Empowering developers with decentralized knowledge sharing.
