# SGN Core Configuration Example
# Copy this file to .env and fill in your values

# GitHub Personal Access Token (required for seeding from GitHub)
# Get one at: https://github.com/settings/tokens
GH_TOKEN="your_github_token_here"

# SGN Daemon Configuration
SGN_DAEMON="http://localhost:8787"
SGN_HTTP_PORT=8787

# Broadcasting (set to 'off' for single-node development)
SGN_BROADCAST="off"

# Storage Paths
SGN_DB="./data/sgn.db.json"
SGN_KUS_DIR="./data/kus"
SGN_LOGS_DIR="./logs"

# Trust Configuration
SGN_TRUST_PATH="./trust.json"

# Development Settings
NODE_ENV="development"
