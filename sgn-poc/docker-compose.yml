version: '3.8'

services:
  # Redis - Hot Cache Tier
  redis:
    image: redis:7-alpine
    container_name: sgn-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Neo4j - Cold Graph Tier
  neo4j:
    image: neo4j:5.15-community
    container_name: sgn-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/sgnpassword
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "sgnpassword", "RETURN 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL - Alternative to SQLite for production
  postgres:
    image: postgres:16-alpine
    container_name: sgn-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=sgn_database
      - POSTGRES_USER=sgn_user
      - POSTGRES_PASSWORD=sgn_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sgn_user -d sgn_database"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Elasticsearch - For advanced search (optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: sgn-elasticsearch
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Grafana - For monitoring and analytics
  grafana:
    image: grafana/grafana:10.2.0
    container_name: sgn-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=sgnpassword
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - postgres
      - redis
      - neo4j

  # Prometheus - For metrics collection
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: sgn-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

volumes:
  redis_data:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  postgres_data:
  elasticsearch_data:
  grafana_data:
  prometheus_data:

networks:
  default:
    name: sgn-network
